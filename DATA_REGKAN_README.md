# Menu Data Regkan

## Deskripsi
Menu "Data Regkan" telah berhasil dibuat untuk menampilkan data tabel dengan 4 kolom sesuai dengan desain yang diminta:

1. **Daftar pasien baru**
2. **Daftar pasien notifikasi kanker**
3. **Daftar pasien registrasi kanker**
4. **Daftar bukan registrasi kanker**

## File yang Dibuat/Dimodifikasi

### 1. Controller
- **File**: `app/Http/Controllers/DataRegkanController.php`
- **Fungsi**: Menangani logika untuk halaman Data Regkan
- **Method**: `index()` - menampilkan data dummy untuk 4 kategori pasien

### 2. View
- **File**: `resources/views/data-regkan/index.blade.php`
- **Fungsi**: Template untuk menampilkan tabel Data Regkan
- **Fitur**:
  - Tabel dengan 4 kolom utama
  - Sub-header untuk No MR dan Nama di setiap kolom
  - Styling khusus dengan warna sesuai desain
  - Responsive design

### 3. Routes
- **File**: `routes/web.php`
- **Route baru**: `GET /data-regkan` → `DataRegkanController@index`
- **Name**: `data-regkan`
- **Middleware**: `auth:pengguna` (hanya user yang login)

### 4. Sidebar Menu
- **File**: `resources/views/layouts/sidebar.blade.php`
- **Penambahan**: Menu "Data Regkan" dengan icon tabel
- **Active state**: Otomatis aktif saat di halaman Data Regkan

## Struktur Tabel

```
┌─────────────────────┬─────────────────────┬─────────────────────┬─────────────────────┐
│ Daftar pasien baru  │ Daftar pasien       │ Daftar pasien       │ Daftar bukan        │
│                     │ notifikasi kanker   │ registrasi kanker   │ registrasi kanker   │
├─────────────────────┼─────────────────────┼─────────────────────┼─────────────────────┤
│ No MR │ Nama        │ No MR │ Nama        │ No MR │ Nama        │ No MR │ Nama        │
├─────────────────────┼─────────────────────┼─────────────────────┼─────────────────────┤
│ 001   │ Ahmad S.    │ 101   │ Maria D.    │ 201   │ Andi P.     │ 301   │ Dewi S.     │
│ 002   │ Siti N.     │ 102   │ Joko W.     │ 202   │ Lestari W.  │ 302   │ Bambang S.  │
│ ...   │ ...         │ ...   │ ...         │ ...   │ ...         │ ...   │ ...         │
└─────────────────────┴─────────────────────┴─────────────────────┴─────────────────────┘
```

## Warna Desain
- **Header utama**: `#4a7c59` (hijau tua)
- **Sub-header**: `#2c5aa0` (biru tua)
- **Baris genap**: `#f8f9fa` (abu-abu muda)

## Data Dummy
Saat ini menggunakan data dummy untuk demonstrasi. Untuk implementasi production, data dapat diambil dari database dengan query yang sesuai di controller.

## Cara Mengakses
1. Login ke aplikasi
2. Klik menu "Data Regkan" di sidebar
3. Atau akses langsung: `http://domain.com/data-regkan`

## Pengembangan Selanjutnya
- Integrasi dengan database real
- Filter dan pencarian data
- Export ke Excel/PDF
- Pagination untuk data besar
- CRUD operations jika diperlukan
