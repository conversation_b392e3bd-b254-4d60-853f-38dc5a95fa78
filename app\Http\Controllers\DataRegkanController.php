<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DataRegkanController extends Controller
{
    /**
     * Display the data regkan page
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Data dummy untuk contoh - nanti bisa diganti dengan query database yang sesuai
        $daftarPasienBaru = [
            ['no_mr' => '001', 'nama' => '<PERSON> Sur<PERSON>'],
            ['no_mr' => '002', 'nama' => 'Siti Nurhaliza'],
            ['no_mr' => '003', 'nama' => '<PERSON><PERSON>'],
            ['no_mr' => '004', 'nama' => '<PERSON>na Sari'],
            ['no_mr' => '005', 'nama' => 'Dedi Kurniawan'],
        ];

        $daftarPasienNotifikasiKanker = [
            ['no_mr' => '101', 'nama' => '<PERSON>'],
            ['no_mr' => '102', 'nama' => '<PERSON><PERSON>'],
            ['no_mr' => '103', 'nama' => '<PERSON><PERSON>'],
            ['no_mr' => '104', 'nama' => 'Agus Salim'],
        ];

        $daftarPasienRegistrasiKanker = [
            ['no_mr' => '201', 'nama' => 'Andi Pratama'],
            ['no_mr' => '202', 'nama' => 'Lestari Wati'],
            ['no_mr' => '203', 'nama' => 'Hendra Gunawan'],
            ['no_mr' => '204', 'nama' => 'Sari Indah'],
            ['no_mr' => '205', 'nama' => 'Budi Hartono'],
            ['no_mr' => '206', 'nama' => 'Nina Sari'],
        ];

        $daftarBukanRegistrasiKanker = [
            ['no_mr' => '301', 'nama' => 'Dewi Sartika'],
            ['no_mr' => '302', 'nama' => 'Bambang Sutrisno'],
            ['no_mr' => '303', 'nama' => 'Indira Sari'],
        ];

        return view('data-regkan.index', compact(
            'daftarPasienBaru',
            'daftarPasienNotifikasiKanker', 
            'daftarPasienRegistrasiKanker',
            'daftarBukanRegistrasiKanker'
        ));
    }
}
