@extends('layouts.app')

@section('title', 'Data Regkan')

@push('styles')
    <!-- DataTables CSS via CDN -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap4.min.css">
@endpush

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Data Regkan</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Data Regkan</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Data Regkan</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="data-regkan-table" class="table table-bordered dt-responsive nowrap w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th class="text-center">Daftar Pasien Baru</th>
                                    <th class="text-center">Daftar Pasien Notifikasi Kanker</th>
                                    <th class="text-center">Daftar Pasien Registrasi Kanker</th>
                                    <th class="text-center">Daftar Bukan Registrasi Kanker</th>
                                </tr>
                                <tr style="background-color: #64748b; color: white;">
                                    <td>
                                        <div class="d-flex">
                                            <div style="width: 30%; font-weight: bold;">No MR</div>
                                            <div style="width: 70%; font-weight: bold;">Nama</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <div style="width: 30%; font-weight: bold;">No MR</div>
                                            <div style="width: 70%; font-weight: bold;">Nama</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <div style="width: 30%; font-weight: bold;">No MR</div>
                                            <div style="width: 70%; font-weight: bold;">Nama</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <div style="width: 30%; font-weight: bold;">No MR</div>
                                            <div style="width: 70%; font-weight: bold;">Nama</div>
                                        </div>
                                    </td>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $maxRows = max(
                                        count($daftarPasienBaru),
                                        count($daftarPasienNotifikasiKanker),
                                        count($daftarPasienRegistrasiKanker),
                                        count($daftarBukanRegistrasiKanker)
                                    );
                                @endphp

                                @for($i = 0; $i < $maxRows; $i++)
                                    <tr>
                                        <td>
                                            @if(isset($daftarPasienBaru[$i]))
                                                <div class="d-flex">
                                                    <div style="width: 30%;">{{ $daftarPasienBaru[$i]->no_mr ?? $daftarPasienBaru[$i]['no_mr'] }}</div>
                                                    <div style="width: 70%;">{{ $daftarPasienBaru[$i]->nama ?? $daftarPasienBaru[$i]['nama'] }}</div>
                                                </div>
                                            @endif
                                        </td>
                                        <td>
                                            @if(isset($daftarPasienNotifikasiKanker[$i]))
                                                <div class="d-flex">
                                                    <div style="width: 30%;">{{ $daftarPasienNotifikasiKanker[$i]['no_mr'] }}</div>
                                                    <div style="width: 70%;">{{ $daftarPasienNotifikasiKanker[$i]['nama'] }}</div>
                                                </div>
                                            @endif
                                        </td>
                                        <td>
                                            @if(isset($daftarPasienRegistrasiKanker[$i]))
                                                <div class="d-flex">
                                                    <div style="width: 30%;">{{ $daftarPasienRegistrasiKanker[$i]['no_mr'] }}</div>
                                                    <div style="width: 70%;">{{ $daftarPasienRegistrasiKanker[$i]['nama'] }}</div>
                                                </div>
                                            @endif
                                        </td>
                                        <td>
                                            @if(isset($daftarBukanRegistrasiKanker[$i]))
                                                <div class="d-flex">
                                                    <div style="width: 30%;">{{ $daftarBukanRegistrasiKanker[$i]['no_mr'] }}</div>
                                                    <div style="width: 70%;">{{ $daftarBukanRegistrasiKanker[$i]['nama'] }}</div>
                                                </div>
                                            @endif
                                        </td>
                                    </tr>
                                @endfor
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <!-- DataTables JS via CDN -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>

    <script>
        $(document).ready(function() {
            // Konfigurasi DataTable untuk tabel utama
            $('#data-regkan-table').DataTable({
                "pageLength": 10,
                "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
                "responsive": true,
                "autoWidth": false,
                "searching": true,
                "ordering": false, // Disable sorting karena struktur tabel khusus
                "language": {
                    "search": "Cari:",
                    "lengthMenu": "Tampilkan _MENU_ data per halaman",
                    "zeroRecords": "Data tidak ditemukan",
                    "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
                    "infoEmpty": "Tidak ada data tersedia",
                    "infoFiltered": "(difilter dari _MAX_ total data)",
                    "paginate": {
                        "first": "Pertama",
                        "last": "Terakhir",
                        "next": "Selanjutnya",
                        "previous": "Sebelumnya"
                    }
                },
                "columnDefs": [
                    { "width": "25%", "targets": [0, 1, 2, 3] }
                ]
            });
        });
    </script>
@endpush
