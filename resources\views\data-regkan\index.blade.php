@extends('layouts.app')

@section('title', 'Data Regkan')

@push('styles')
    <!-- DataTables CSS via CDN -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap4.min.css">
@endpush

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Data Regkan</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Data Regkan</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Daftar Pasien Baru -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-header" style="background-color: #4a7c59; color: white;">
                    <h4 class="card-title mb-0">Daftar Pasien Baru</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="pasien-baru-table" class="table table-bordered dt-responsive nowrap w-100">
                            <thead>
                                <tr style="background-color: #2c5aa0; color: white;">
                                    <th>No MR</th>
                                    <th>Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($daftarPasienBaru as $pasien)
                                    <tr>
                                        <td>{{ $pasien->no_mr ?? $pasien['no_mr'] }}</td>
                                        <td>{{ $pasien->nama ?? $pasien['nama'] }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Daftar Pasien Notifikasi Kanker -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-header" style="background-color: #4a7c59; color: white;">
                    <h4 class="card-title mb-0">Daftar Pasien Notifikasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="pasien-notifikasi-table" class="table table-bordered dt-responsive nowrap w-100">
                            <thead>
                                <tr style="background-color: #2c5aa0; color: white;">
                                    <th>No MR</th>
                                    <th>Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($daftarPasienNotifikasiKanker as $pasien)
                                    <tr>
                                        <td>{{ $pasien['no_mr'] }}</td>
                                        <td>{{ $pasien['nama'] }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Daftar Pasien Registrasi Kanker -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-header" style="background-color: #4a7c59; color: white;">
                    <h4 class="card-title mb-0">Daftar Pasien Registrasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="pasien-registrasi-table" class="table table-bordered dt-responsive nowrap w-100">
                            <thead>
                                <tr style="background-color: #2c5aa0; color: white;">
                                    <th>No MR</th>
                                    <th>Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($daftarPasienRegistrasiKanker as $pasien)
                                    <tr>
                                        <td>{{ $pasien['no_mr'] }}</td>
                                        <td>{{ $pasien['nama'] }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Daftar Bukan Registrasi Kanker -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-header" style="background-color: #4a7c59; color: white;">
                    <h4 class="card-title mb-0">Daftar Bukan Registrasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="bukan-registrasi-table" class="table table-bordered dt-responsive nowrap w-100">
                            <thead>
                                <tr style="background-color: #2c5aa0; color: white;">
                                    <th>No MR</th>
                                    <th>Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($daftarBukanRegistrasiKanker as $pasien)
                                    <tr>
                                        <td>{{ $pasien['no_mr'] }}</td>
                                        <td>{{ $pasien['nama'] }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <!-- DataTables JS via CDN -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>

    <script>
        $(document).ready(function() {
            // Konfigurasi DataTable untuk semua tabel
            var dataTableConfig = {
                "pageLength": 10,
                "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
                "responsive": true,
                "autoWidth": false,
                "language": {
                    "search": "Cari:",
                    "lengthMenu": "Tampilkan _MENU_ data per halaman",
                    "zeroRecords": "Data tidak ditemukan",
                    "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
                    "infoEmpty": "Tidak ada data tersedia",
                    "infoFiltered": "(difilter dari _MAX_ total data)",
                    "paginate": {
                        "first": "Pertama",
                        "last": "Terakhir",
                        "next": "Selanjutnya",
                        "previous": "Sebelumnya"
                    }
                },
                "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                       '<"row"<"col-sm-12"tr>>' +
                       '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                "columnDefs": [
                    { "width": "25%", "targets": 0 },
                    { "width": "75%", "targets": 1 }
                ]
            };

            // Inisialisasi DataTable untuk setiap tabel
            $('#pasien-baru-table').DataTable(dataTableConfig);
            $('#pasien-notifikasi-table').DataTable(dataTableConfig);
            $('#pasien-registrasi-table').DataTable(dataTableConfig);
            $('#bukan-registrasi-table').DataTable(dataTableConfig);
        });
    </script>
@endpush
