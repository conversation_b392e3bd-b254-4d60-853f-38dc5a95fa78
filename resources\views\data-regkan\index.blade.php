@extends('layouts.app')

@section('title', 'Data Regkan')

@push('styles')
    <!-- DataTables CSS -->
    <link href="{{ asset('assets/libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('assets/libs/datatables.net-buttons-bs4/css/buttons.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('assets/libs/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />

    <style>
        .regkan-table {
            border-collapse: collapse;
            width: 100%;
        }

        .regkan-table th,
        .regkan-table td {
            border: 1px solid #dee2e6;
            text-align: left;
            vertical-align: top;
        }

        .regkan-table th {
            font-weight: bold;
            text-align: center;
        }

        .regkan-table .inner-table {
            width: 100%;
            border: none;
        }

        .regkan-table .inner-table td {
            border: none;
            padding: 4px 8px;
        }

        .regkan-table .data-row {
            min-height: 40px;
        }

        .regkan-table .data-row:nth-child(even) {
            background-color: #f8f9fa;
        }
    </style>
@endpush

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Data Regkan</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Data Regkan</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <!-- Tabel dengan 4 kolom sesuai gambar -->
                    <div class="table-responsive">
                        <table class="regkan-table">
                            <thead>
                                <tr>
                                    <th style="background-color: #4a7c59; color: white; padding: 12px;">Daftar pasien baru</th>
                                    <th style="background-color: #4a7c59; color: white; padding: 12px;">Daftar pasien notifikasi kanker</th>
                                    <th style="background-color: #4a7c59; color: white; padding: 12px;">Daftar pasien registrasi kanker</th>
                                    <th style="background-color: #4a7c59; color: white; padding: 12px;">Daftar bukan registrasi kanker</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Header sub-tabel -->
                                <tr style="background-color: #2c5aa0; color: white;">
                                    <td style="padding: 8px;">
                                        <table class="inner-table">
                                            <tr>
                                                <td style="width: 25%; font-weight: bold;">No MR</td>
                                                <td style="width: 75%; font-weight: bold;">Nama</td>
                                            </tr>
                                        </table>
                                    </td>
                                    <td style="padding: 8px;">
                                        <table class="inner-table">
                                            <tr>
                                                <td style="width: 25%; font-weight: bold;">No MR</td>
                                                <td style="width: 75%; font-weight: bold;">Nama</td>
                                            </tr>
                                        </table>
                                    </td>
                                    <td style="padding: 8px;">
                                        <table class="inner-table">
                                            <tr>
                                                <td style="width: 25%; font-weight: bold;">No MR</td>
                                                <td style="width: 75%; font-weight: bold;">Nama</td>
                                            </tr>
                                        </table>
                                    </td>
                                    <td style="padding: 8px;">
                                        <table class="inner-table">
                                            <tr>
                                                <td style="width: 25%; font-weight: bold;">No MR</td>
                                                <td style="width: 75%; font-weight: bold;">Nama</td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                
                                <!-- Data rows -->
                                @php
                                    $maxRows = max(
                                        count($daftarPasienBaru),
                                        count($daftarPasienNotifikasiKanker),
                                        count($daftarPasienRegistrasiKanker),
                                        count($daftarBukanRegistrasiKanker)
                                    );
                                @endphp
                                
                                @for($i = 0; $i < $maxRows; $i++)
                                    <tr class="data-row">
                                        <td style="padding: 8px;">
                                            @if(isset($daftarPasienBaru[$i]))
                                                <table class="inner-table">
                                                    <tr>
                                                        <td style="width: 25%;">{{ $daftarPasienBaru[$i]['no_mr'] }}</td>
                                                        <td style="width: 75%;">{{ $daftarPasienBaru[$i]['nama'] }}</td>
                                                    </tr>
                                                </table>
                                            @endif
                                        </td>
                                        <td style="padding: 8px;">
                                            @if(isset($daftarPasienNotifikasiKanker[$i]))
                                                <table class="inner-table">
                                                    <tr>
                                                        <td style="width: 25%;">{{ $daftarPasienNotifikasiKanker[$i]['no_mr'] }}</td>
                                                        <td style="width: 75%;">{{ $daftarPasienNotifikasiKanker[$i]['nama'] }}</td>
                                                    </tr>
                                                </table>
                                            @endif
                                        </td>
                                        <td style="padding: 8px;">
                                            @if(isset($daftarPasienRegistrasiKanker[$i]))
                                                <table class="inner-table">
                                                    <tr>
                                                        <td style="width: 25%;">{{ $daftarPasienRegistrasiKanker[$i]['no_mr'] }}</td>
                                                        <td style="width: 75%;">{{ $daftarPasienRegistrasiKanker[$i]['nama'] }}</td>
                                                    </tr>
                                                </table>
                                            @endif
                                        </td>
                                        <td style="padding: 8px;">
                                            @if(isset($daftarBukanRegistrasiKanker[$i]))
                                                <table class="inner-table">
                                                    <tr>
                                                        <td style="width: 25%;">{{ $daftarBukanRegistrasiKanker[$i]['no_mr'] }}</td>
                                                        <td style="width: 75%;">{{ $daftarBukanRegistrasiKanker[$i]['nama'] }}</td>
                                                    </tr>
                                                </table>
                                            @endif
                                        </td>
                                    </tr>
                                @endfor
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <!-- DataTables JS -->
    <script src="{{ asset('assets/libs/datatables.net/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js') }}"></script>
@endpush
