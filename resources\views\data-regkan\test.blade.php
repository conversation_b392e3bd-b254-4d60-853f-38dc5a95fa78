<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Data Regkan</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">
</head>
<body>
    <div class="container-fluid mt-4">
        <h2 class="mb-4">Data Regkan - Test Page</h2>
        
        <div class="row">
            <!-- Daftar Pasien Baru -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card">
                    <div class="card-header" style="background-color: #4a7c59; color: white;">
                        <h5 class="card-title mb-0">Daftar Pasien Baru</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="pasien-baru-table" class="table table-bordered table-striped">
                                <thead>
                                    <tr style="background-color: #2c5aa0; color: white;">
                                        <th>No MR</th>
                                        <th>Nama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($daftarPasienBaru as $pasien)
                                        <tr>
                                            <td>{{ $pasien->no_mr ?? $pasien['no_mr'] }}</td>
                                            <td>{{ $pasien->nama ?? $pasien['nama'] }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Daftar Pasien Notifikasi Kanker -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card">
                    <div class="card-header" style="background-color: #4a7c59; color: white;">
                        <h5 class="card-title mb-0">Daftar Pasien Notifikasi Kanker</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="pasien-notifikasi-table" class="table table-bordered table-striped">
                                <thead>
                                    <tr style="background-color: #2c5aa0; color: white;">
                                        <th>No MR</th>
                                        <th>Nama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($daftarPasienNotifikasiKanker as $pasien)
                                        <tr>
                                            <td>{{ $pasien['no_mr'] }}</td>
                                            <td>{{ $pasien['nama'] }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Daftar Pasien Registrasi Kanker -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card">
                    <div class="card-header" style="background-color: #4a7c59; color: white;">
                        <h5 class="card-title mb-0">Daftar Pasien Registrasi Kanker</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="pasien-registrasi-table" class="table table-bordered table-striped">
                                <thead>
                                    <tr style="background-color: #2c5aa0; color: white;">
                                        <th>No MR</th>
                                        <th>Nama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($daftarPasienRegistrasiKanker as $pasien)
                                        <tr>
                                            <td>{{ $pasien['no_mr'] }}</td>
                                            <td>{{ $pasien['nama'] }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Daftar Bukan Registrasi Kanker -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card">
                    <div class="card-header" style="background-color: #4a7c59; color: white;">
                        <h5 class="card-title mb-0">Daftar Bukan Registrasi Kanker</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="bukan-registrasi-table" class="table table-bordered table-striped">
                                <thead>
                                    <tr style="background-color: #2c5aa0; color: white;">
                                        <th>No MR</th>
                                        <th>Nama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($daftarBukanRegistrasiKanker as $pasien)
                                        <tr>
                                            <td>{{ $pasien['no_mr'] }}</td>
                                            <td>{{ $pasien['nama'] }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            // Konfigurasi DataTable
            var dataTableConfig = {
                "pageLength": 5,
                "lengthMenu": [[5, 10, 25, -1], [5, 10, 25, "All"]],
                "responsive": true,
                "autoWidth": false,
                "language": {
                    "search": "Cari:",
                    "lengthMenu": "Tampilkan _MENU_ data per halaman",
                    "zeroRecords": "Data tidak ditemukan",
                    "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
                    "infoEmpty": "Tidak ada data tersedia",
                    "infoFiltered": "(difilter dari _MAX_ total data)",
                    "paginate": {
                        "first": "Pertama",
                        "last": "Terakhir",
                        "next": "Selanjutnya",
                        "previous": "Sebelumnya"
                    }
                }
            };

            // Inisialisasi DataTable untuk setiap tabel
            $('#pasien-baru-table').DataTable(dataTableConfig);
            $('#pasien-notifikasi-table').DataTable(dataTableConfig);
            $('#pasien-registrasi-table').DataTable(dataTableConfig);
            $('#bukan-registrasi-table').DataTable(dataTableConfig);
        });
    </script>
</body>
</html>
