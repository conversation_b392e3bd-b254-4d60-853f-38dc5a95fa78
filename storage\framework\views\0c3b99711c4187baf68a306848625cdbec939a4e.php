<?php $__env->startSection('title', 'Data Regkan'); ?>

<?php $__env->startPush('styles'); ?>
    <!-- Custom styles for search -->
    <style>
        .table-responsive {
            border-radius: 0.375rem;
        }
        .input-group-text {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Data Regkan</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Data Regkan</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Data Regkan</h4>
                </div>
                <div class="card-body">
                    <!-- Search Input -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="ri-search-line"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="Cari data...">
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table id="dataTable" class="table table-bordered dt-responsive nowrap w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th class="text-center">Daftar Pasien Baru</th>
                                    <th class="text-center">Daftar Pasien Notifikasi Kanker</th>
                                    <th class="text-center">Daftar Pasien Registrasi Kanker</th>
                                    <th class="text-center">Daftar Bukan Registrasi Kanker</th>
                                </tr>
                                <tr style="background-color: #64748b; color: white;">
                                    <td>
                                        <div class="d-flex">
                                            <div style="width: 30%; font-weight: bold;">No MR</div>
                                            <div style="width: 70%; font-weight: bold;">Nama</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <div style="width: 30%; font-weight: bold;">No MR</div>
                                            <div style="width: 70%; font-weight: bold;">Nama</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <div style="width: 30%; font-weight: bold;">No MR</div>
                                            <div style="width: 70%; font-weight: bold;">Nama</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <div style="width: 30%; font-weight: bold;">No MR</div>
                                            <div style="width: 70%; font-weight: bold;">Nama</div>
                                        </div>
                                    </td>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    $maxRows = max(
                                        count($daftarPasienBaru),
                                        count($daftarPasienNotifikasiKanker),
                                        count($daftarPasienRegistrasiKanker),
                                        count($daftarBukanRegistrasiKanker)
                                    );
                                ?>

                                <?php for($i = 0; $i < $maxRows; $i++): ?>
                                    <tr>
                                        <td>
                                            <?php if(isset($daftarPasienBaru[$i])): ?>
                                                <div class="d-flex">
                                                    <div style="width: 30%;"><?php echo e($daftarPasienBaru[$i]->no_mr ?? $daftarPasienBaru[$i]['no_mr']); ?></div>
                                                    <div style="width: 70%;"><?php echo e($daftarPasienBaru[$i]->nama ?? $daftarPasienBaru[$i]['nama']); ?></div>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if(isset($daftarPasienNotifikasiKanker[$i])): ?>
                                                <div class="d-flex">
                                                    <div style="width: 30%;"><?php echo e($daftarPasienNotifikasiKanker[$i]['no_mr']); ?></div>
                                                    <div style="width: 70%;"><?php echo e($daftarPasienNotifikasiKanker[$i]['nama']); ?></div>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if(isset($daftarPasienRegistrasiKanker[$i])): ?>
                                                <div class="d-flex">
                                                    <div style="width: 30%;"><?php echo e($daftarPasienRegistrasiKanker[$i]['no_mr']); ?></div>
                                                    <div style="width: 70%;"><?php echo e($daftarPasienRegistrasiKanker[$i]['nama']); ?></div>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if(isset($daftarBukanRegistrasiKanker[$i])): ?>
                                                <div class="d-flex">
                                                    <div style="width: 30%;"><?php echo e($daftarBukanRegistrasiKanker[$i]['no_mr']); ?></div>
                                                    <div style="width: 70%;"><?php echo e($daftarBukanRegistrasiKanker[$i]['nama']); ?></div>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endfor; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <!-- jQuery sudah tersedia dari template -->

    <script>
        $(document).ready(function() {
            // Simple search functionality
            $('#searchInput').on('keyup', function() {
                var value = $(this).val().toLowerCase();
                $('#dataTable tbody tr').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\regkan\resources\views/data-regkan/index.blade.php ENDPATH**/ ?>